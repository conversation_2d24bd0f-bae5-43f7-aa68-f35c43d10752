{ config, pkgs, ... }:

let
  unstable = import <nixos-unstable> {
    config = config.nixpkgs.config;
  };
in

{
  imports = [
    ./hardware-configuration.nix
    <home-manager/nixos>
  ];

  # Hardware Configuration
  hardware.sensor.iio.enable = true;

  # System Configuration
  system.stateVersion = "24.05";
  system.autoUpgrade = {
    enable = true;
    allowReboot = false;
    dates = "00:00:00";
    persistent = true;
  };

  nix.gc = {
    automatic = true;
    dates = "weekly";
    options = "--delete-older-than 7d";
  };

  boot = {
    loader = {
      systemd-boot.enable = true;
      efi.canTouchEfiVariables = true;
    };
    kernelPackages = pkgs.linuxPackages_zen;
  };

  # Network Configuration
  networking = {
    hostName = "iDell";
    networkmanager.enable = true;
    firewall = {
      allowedTCPPorts = [ 22000 8384 1714 1764 8080 39630 54104 11434 9000 8000 ];
      allowedUDPPorts = [ 22000 21027 1714 1764 1716 ];
    };
  };

  # Localization
  time.timeZone = "Africa/Algiers";
  i18n = {
    defaultLocale = "en_US.UTF-8";
    extraLocaleSettings = {
      LC_ADDRESS = "ar_DZ.UTF-8";
      LC_IDENTIFICATION = "ar_DZ.UTF-8";
      LC_MEASUREMENT = "ar_DZ.UTF-8";
      LC_MONETARY = "ar_DZ.UTF-8";
      LC_NAME = "ar_DZ.UTF-8";
      LC_NUMERIC = "ar_DZ.UTF-8";
      LC_PAPER = "ar_DZ.UTF-8";
      LC_TELEPHONE = "ar_DZ.UTF-8";
      LC_TIME = "ar_DZ.UTF-8";
    };
  };

  # Desktop Environment
  services.xserver = {
    enable = true;
    xkb = {
      layout = "fr";
      variant = "";
    };
  };
  services.displayManager.sddm.enable = true;
  services.desktopManager.plasma6.enable = true;

  # Enable KDE Applications
  programs.kdeconnect.enable = true;
  environment.plasma6.excludePackages = [];
  programs.partition-manager.enable = true;

  # Configure console keymap
  console.keyMap = "fr";

  # System Services
  services = {
    printing.enable = false;
    libinput.enable = true;
    openssh.enable = true;
    fprintd.enable = true;

    pipewire = {
      enable = true;
      alsa = {
        enable = true;
        support32Bit = true;
      };
      pulse.enable = true;
    };

    power-profiles-daemon.enable = true;

    logind.extraConfig = ''
      HandleLidSwitch=ignore
      HandleLidSwitchDocked=ignore
      HandleLidSwitchExternalPower=ignore
    '';

    upower = {
      enable = true;
      criticalPowerAction = "Hibernate";
    };

  };

  # User Configuration
  users.users.yusuf = {
    isNormalUser = true;
    description = "yusuf";
    extraGroups = [ "networkmanager" "wheel" "fprintd" "adbusers" "docker" ];
    shell = pkgs.zsh;
  };

  # System Programs and Features
  environment.systemPackages = with pkgs; [
    fprintd
  ];

  programs = {
    zsh.enable = true;
    adb.enable = true;
    nix-ld.enable = true;
  };

  security.rtkit.enable = true;

  nixpkgs.config = {
    allowUnfree = true;
    android_sdk.accept_license = true;
  };

  fonts = {
    enableDefaultPackages = true;
    packages = with pkgs; [
      noto-fonts
      noto-fonts-cjk-sans
      noto-fonts-emoji
      noto-fonts-extra
      corefonts
      powerline-fonts
    ];
    fontconfig = {
      defaultFonts = {
        serif = [ "Noto Serif" ];
        sansSerif = [ "Noto Sans" ];
        monospace = [ "Noto Sans Mono" ];
        emoji = [ "Noto Color Emoji" ];
      };
    };
  };

  # Home Manager Configuration
  home-manager = {
    backupFileExtension = "backup";
    users.yusuf = { pkgs, ... }: {
      nixpkgs.config.allowUnfree = true;
      home.stateVersion = "25.05";
      home.packages = with pkgs; [
        unstable.vscode
        nixd
        nixpkgs-fmt
        kdePackages.applet-window-buttons6
        kdePackages.kdeconnect-kde
        backintime
        sshfs
        vlc
        libreoffice
        obsidian
        scrcpy
        usbimager
        qbittorrent-enhanced
        (writeScriptBin "retry-until-success" ''
          #!${pkgs.zsh}/bin/zsh
          if [ $# -eq 0 ]; then
              echo "Usage: $0 'command to execute'"
              exit 1
          fi
          command_to_run="$@"
          attempt=1
          echo "Starting to execute: $command_to_run"
          while true; do
              # Execute the command
              if eval "$command_to_run"; then
                  echo "Command succeeded on attempt $attempt"
                  exit 0
              else
                  echo "Attempt $attempt failed. Retrying..."
                  attempt=$((attempt + 1))
                  sleep 1
              fi
          done
        '')
      ];

      home.keyboard = {
        layout = "fr,ara";
        options = ["grp:alt_shift_toggle"]; # Use Alt+Shift to switch layouts
      };

      programs = {
      browserpass.enable = true;
      brave = {
        enable = true;
        extensions = [
          { id = "cjpalhdlnbpafiamejdnhcphjbkeiagm"; } # uBlock Origin
          { id = "eimadpbcbfnmbkopoojfekhnkhdbieeh"; } # Dark Reader
          { id = "nngceckbapebfimnlniiiahkandclblb"; } # Bitwarden
          { id = "kchgllkpfcggmdaoopkhlkbcokngahlg"; } # DF Tube
          { id = "pehaalcefcjfccdpbckoablngfkfgfgj"; } # Block image
        ];
      };

        git = {
          enable = true;
          extraConfig = {
            credential.helper = "store";
            user = {
              name = "Yusuf";
              email = "<EMAIL>";
            };
          };
        };

        zsh = {
          enable = true;
          autosuggestion.enable = true;
          enableCompletion = true;
          syntaxHighlighting.enable = true;

          oh-my-zsh = {
            enable = true;
            theme = "agnoster";
            plugins = [
              "git"
              "docker"
              "docker-compose"
              "sudo"
              "history"
              "dirhistory"
            ];
          };

          shellAliases = {
            switch = "sudo nixos-rebuild switch";
            upgrade = "sudo nixos-rebuild switch --upgrade";
            pod = "scrcpy --new-display=1920x1080/240 --start-app=com.itunestoppodcastplayer.app";
            ls = "ls --color=auto";
            ll = "ls -la";
            retry = "retry-until-success";
          };

          initExtra = ''
            # Set history size and file
            HISTSIZE=10000
            SAVEHIST=10000
            HISTFILE=~/.zsh_history

            # Basic auto/tab completion
            autoload -U compinit
            zstyle ':completion:*' menu select
            zmodload zsh/complist
            compinit
            _comp_options+=(globdots)

            # Enable searching through history
            bindkey '^r' history-incremental-pattern-search-backward
          '';
        };
      };
    };
  };
}
